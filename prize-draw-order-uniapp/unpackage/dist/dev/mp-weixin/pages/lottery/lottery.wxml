<scroll-view class="lottery-container data-v-557dc19a" style="{{('background:'+backgroundGradient)}}" scroll-y="true" enable-back-to-top="true" scroll-with-animation="{{true}}"><block wx:if="{{isLoading}}"><view class="loading-container data-v-557dc19a"><view class="loading-icon data-v-557dc19a">⏳</view><view class="loading-text data-v-557dc19a">加载中...</view></view></block><block wx:if="{{merchantInfo&&!isLoading}}"><view class="merchant-header data-v-557dc19a"><block wx:if="{{currentActivity&&currentActivity.activityDesc}}"><view class="activity-desc data-v-557dc19a">{{currentActivity.activityDesc}}</view></block></view></block><block wx:if="{{!isLoading}}"><block wx:if="{{!currentActivity}}"><view class="no-activity-tip data-v-557dc19a"><view class="tip-icon data-v-557dc19a">📢</view><view class="tip-title data-v-557dc19a">暂无抽奖活动</view><view class="tip-desc data-v-557dc19a">当前没有进行中的抽奖活动，请稍后再试</view></view></block><block wx:if="{{remainingDraws>0&&currentActivity}}"><view class="lottery-grid-container data-v-557dc19a"><view class="grid-wrapper data-v-557dc19a"><view class="lottery-grid data-v-557dc19a"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" data-event-params="{{({index})}}" class="{{['grid-item','data-v-557dc19a',(currentIndex===index)?'active':'',(index===4)?'center':'']}}" style="{{(index===4?'background:'+gridContainerBg:'')}}" bindtap="__e"><block wx:if="{{index===4}}"><view class="center-button data-v-557dc19a"><view class="center-text data-v-557dc19a">{{isDrawing?'抽奖中...':'点击抽奖'}}</view><view class="remaining-text data-v-557dc19a">{{item.m0}}</view></view></block><block wx:else><block wx:if="{{item.$orig}}"><view class="prize-item data-v-557dc19a"><view class="prize-icon data-v-557dc19a"><block wx:if="{{item.$orig.prizeImage}}"><image class="prize-image data-v-557dc19a" src="{{item.m1}}" mode="aspectFit"></image></block><block wx:else><text class="default-icon data-v-557dc19a">🎁</text></block></view><view class="prize-name data-v-557dc19a">{{item.$orig.prizeName}}</view></view></block></block></view></block></view></view></view></block><block wx:if="{{remainingDraws<=0&&currentActivity&&lastWinningRecord&&showLastWinning}}"><view data-event-opts="{{[['tap',[['hideLastWinning',['$event']]]]]}}" class="last-winning-fullscreen data-v-557dc19a" bindtap="__e"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="last-winning-display data-v-557dc19a" style="{{'background:'+(backgroundGradient)+';'}}" catchtap="__e"><view data-event-opts="{{[['tap',[['hideLastWinning',['$event']]]]]}}" class="close-btn data-v-557dc19a" bindtap="__e"><text class="close-icon data-v-557dc19a">×</text></view><view class="winning-animation data-v-557dc19a"><view class="fireworks data-v-557dc19a"><block wx:for="{{fireworkStyles}}" wx:for-item="style" wx:for-index="index" wx:key="index"><view class="firework data-v-557dc19a" style="{{(style)}}"></view></block></view><view class="winning-icon data-v-557dc19a"><text class="icon-text data-v-557dc19a">🎉</text></view><view class="congratulations data-v-557dc19a"><text class="congrats-text data-v-557dc19a">上次中奖记录</text></view><view class="prize-info data-v-557dc19a"><view class="prize-name data-v-557dc19a">{{lastWinningRecord.prizeName}}</view><block wx:if="{{lastWinningRecord.prizeDesc}}"><view class="prize-desc data-v-557dc19a">{{lastWinningRecord.prizeDesc}}</view></block></view><view class="draw-time data-v-557dc19a"><text class="data-v-557dc19a">{{"抽奖时间："+$root.m2}}</text></view><block wx:if="{{currentActivity&&currentActivity.claimInstruction}}"><view class="claim-instruction data-v-557dc19a"><view class="instruction-title data-v-557dc19a">领取说明</view><view class="instruction-content data-v-557dc19a">{{currentActivity.claimInstruction}}</view></view></block><block wx:if="{{currentActivity&&currentActivity.wechatQrcode}}"><view class="wechat-qrcode data-v-557dc19a"><image class="qrcode-img data-v-557dc19a" src="{{$root.m3}}" mode="aspectFit" data-event-opts="{{[['error',[['handleImageError',['$event']]]],['load',[['handleImageLoad',['$event']]]]]}}" binderror="__e" bindload="__e"></image><block wx:if="{{imageLoadError}}"><view class="qrcode-error data-v-557dc19a"><text class="data-v-557dc19a">二维码加载失败</text></view></block></view></block></view></view></view></block><block wx:if="{{remainingDraws>0&&currentActivity&&currentActivity.drawRules}}"><view class="lottery-rules data-v-557dc19a"><view class="rules-title data-v-557dc19a">抽奖规则</view><view class="rules-content data-v-557dc19a">{{currentActivity.drawRules}}</view></view></block></block></scroll-view>